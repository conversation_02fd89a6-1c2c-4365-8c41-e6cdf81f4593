
var styleTag = document.createElement('link');
styleTag.id = 'custom-styles';
styleTag.rel = 'stylesheet';
styleTag.href = `css/vwtopx.css?v=${Math.floor(Math.random()*10000)}`;
function handleAspectRatio() {
  var screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  var screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  var aspectRatio = screenWidth / screenHeight;
  var pagestyle = document.querySelector('link#custom-styles')
  if (aspectRatio > 0.5997001499250375) {
    if(!pagestyle){
      document.head.appendChild(styleTag)
    }
  } else {
    if(pagestyle){
      pagestyle.parentNode.removeChild(pagestyle)
    }
  }
  
}
window.addEventListener('load', handleAspectRatio);
window.addEventListener('resize', e=>{
  if(!["input","textarea"].includes(document.activeElement.tagName.toLowerCase())){
    handleAspectRatio()
  }
});
