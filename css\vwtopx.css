@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  top: 2vh;
  right: 2vh;
  background: url(../img/music.png) no-repeat center center / contain;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 102vh;
  height: 100vh;
  width: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(180deg, #2a3d87 0%, #ee7c47 100%);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #352219;
  background: url(../img/bj.jpg) no-repeat center center / 60vh auto;
}
.warp .page .rotation {
  width: 39vh;
  height: 5vh;
  position: absolute;
  top: 6vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
}
.warp .page .rotation .van-swipe {
  height: 5vh;
}
.warp .page .rotation .van-swipe .van-swipe-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.warp .page .rotation p {
  text-align: center;
  white-space: nowrap;
  font-size: 2vh;
}
.warp .page .rotation p span {
  font-size: 2vh;
}
.warp .page .title {
  margin-top: 12vh;
  width: 47vh;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 2vh;
  width: 36vh;
  z-index: 3;
}
.warp .page .start {
  margin-top: 43vh;
  width: 25vh;
  height: 12vh;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .bg2 {
  width: 60vh;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .bg3 {
  width: 60vh;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: relative;
  z-index: 2;
  margin-top: 3vh;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 1vh;
}
.warp .page .button_container .button {
  width: 21vh;
}
.warp .bj2 {
  background-image: url(../img/bj2.jpg);
}
.warp .bj3 {
  background-image: url(../img/bj3.jpg);
}
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.9);
  }
}
@keyframes scoreChangeAnimation {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translateX(-50%) translateY(-3vh) scale(1.2);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-6vh) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-12vh) scale(0.8);
  }
}
.blur {
  filter: blur(1vh);
}
.fc {
  justify-content: center;
}
.area {
  margin-top: -5vh;
  width: 56vh;
  height: 76vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  background: url(../img/area.png) no-repeat center center / 100% 100%;
}
.area .stit {
  margin-top: 6vh;
  width: 28vh;
}
.area .spirit1 {
  position: absolute;
  width: 18vh;
  left: -5vh;
  bottom: -4vh;
}
.area .back {
  position: absolute;
  bottom: -2vh;
  width: 27vh;
}
.area .submit {
  position: absolute;
  bottom: -9vh;
  width: 24vh;
}
.area .rule {
  width: 100%;
  padding: 0 2vh;
  margin: 1vh 0 6vh;
  flex: 1;
  overflow-y: auto;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: -0vh;
  position: relative;
}
.area .prize {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .prize .mt5 {
  margin-top: 1vh;
}
.area .prize .info {
  padding: 3vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 10vh;
  width: 42vh;
}
.area .prize .info:first-child {
  border-bottom: 1px dashed #215444;
}
.area .prize .info .p2 {
  font-size: 3vh;
  line-height: 4vh;
  max-width: 45vh;
  text-align: center;
}
.area .prize .info .jptit {
  width: 18vh;
  margin-bottom: 1vh;
}
.area .prize .edit {
  width: 27vh;
}
.area .form {
  width: 100%;
  padding: 10vh 3vh 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item {
  margin-left: 0;
  margin-bottom: 3vh;
  display: flex;
  align-items: center;
}
.area .form .form-item label {
  width: 13vh;
  font-weight: bold;
  font-size: 3vh;
  white-space: nowrap;
  color: #352219;
  flex-shrink: 0;
}
.area .form .form-item div input {
  margin-bottom: 2vh;
}
.area .form .form-item div input:nth-last-child(1) {
  margin-bottom: 0;
}
.area .form .form-item .right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.area .form .form-item input {
  margin-left: 0vh;
  padding-left: 2vh;
  width: 30vh;
  height: 5vh;
  border: 1px #352219 solid;
  flex-shrink: 0;
  opacity: 1;
  color: #352219;
  font-size: 3vh;
}
.area .form .form-item input::-webkit-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input::-moz-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item input:-ms-input-placeholder {
  color: #352219;
  opacity: 0.6;
}
.area .form .form-item #getArea {
  opacity: 0;
  position: absolute;
  z-index: -1;
}
.area .form .form-footer {
  margin-top: -6vh;
  display: flex;
  width: 200%;
  transform: scale(0.5);
  color: #352219;
}
.area .form .form-footer .fz1 {
  font-size: 4vh;
}
.area .form .form-footer p {
  font-size: 4vh;
  line-height: 1.5;
  text-align: justify;
  letter-spacing: 0vh;
}
.area .form .button {
  margin-top: -3vh;
  width: 18vh;
}
.area .form .fs {
  align-items: flex-start;
}
.area .form .fs label {
  margin-top: 0vh;
}
.area2 {
  margin-top: 0;
  width: 52vh;
  height: 82vh;
  background: url(../img/area2.png) no-repeat center center / 100% 100%;
}
.area2 .stit {
  margin-top: -13vh;
  margin-bottom: 6vh;
}
.area2 .back {
  bottom: 5vh;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #352219;
  font-weight: 300;
}
.mask .popup {
  margin-top: -1vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 26vh;
  position: absolute;
  bottom: -4vh;
}
.mask .popup1 {
  width: 53vh;
  height: 41vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  width: 45vh;
  height: 39vh;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .close2 {
  width: 6vh;
  position: absolute;
  top: 0;
  right: -1vh;
}
.mask .popup3 {
  width: 45vh;
  height: 36vh;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .back {
  bottom: 5vh;
}
.mask .popup4 {
  width: 45vh;
  height: 36vh;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup4 .back {
  bottom: 5vh;
}
.mask .popup5 {
  width: 45vh;
  height: 40vh;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
  padding-top: 0vh;
}
.mask .popup5 .p3 {
  margin-top: -2vh;
  font-size: 4vh;
  white-space: nowrap;
}
.mask .popup5 .p4 {
  font-size: 4vh;
  white-space: nowrap;
}
.mask .popup5 .back {
  bottom: 5vh;
}
.mask .popup6 {
  width: 45vh;
  height: 40vh;
  background: url(../img/popup6.png) no-repeat center top / 100% 100%;
}
.mask .popup6 .back {
  bottom: 5vh;
}
