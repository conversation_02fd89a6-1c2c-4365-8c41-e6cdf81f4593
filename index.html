<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>大方爱生活美味串一串</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <div class="rotation animate__animated animate__fadeIn">
                <van-swipe ref="swipe" class="my-swipe" :autoplay="2000" :show-indicators="false" indicator-color="white" vertical>
                    <van-swipe-item v-for="(item,index) in handleRotation">
                        <p>{{item[0]}}<span>{{item[1]}}</span>{{item[2]}}</p>
                    </van-swipe-item>
                </van-swipe>
            </div>
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <div class="start" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="page=3">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area1">
                <div class="rule" v-html="startData.rule"></div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 我的奖品页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/title.png" class="title2 animate__animated animate__zoomIn">
            <div class="area area2">
                <div class="prize">
                    <div class="info">
                        <img src="img/jptit1.png" class="jptit mt5">
                        <p class="p2 p3 mt5" v-if="startData.prize">
                            {{startData.prizeTime}}：{{startData.ad}}</p>
                        <div class="p2" v-if="startData.prize">{{startData.prize}}</div>
                        <p class="p2 p1 mt5" v-else>暂未中奖</p>
                    </div>
                    <div class="info">
                        <img src="img/jptit2.png" class="jptit">
                        <p class="p2 mt5">{{startData.userInfo.name}}&nbsp;&nbsp;{{startData.userInfo.phone}}</p>
                        <p class="p2">{{startData.userInfo.area.split(',').join('')}}</p>
                        <p class="p2">{{startData.userInfo.address}}</p>
                    </div>
                    <img src="img/edit.png" class="edit" @click="edit">
                </div>
                <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
            </div>
        </div>
        <!-- 登记信息页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <div class="area area3">
                <form class="form">
                    <div class="form-item">
                        <label>姓　　名:</label>
                        <input type="text" v-model="form.name">
                    </div>
                    <div class="form-item">
                        <label>联系方式:</label>
                        <input type="number" v-model="form.phone">
                    </div>
                    <div class="form-item fs">
                        <label>邮寄地址:</label>
                        <div class="right" @click="focus">
                            <input type="text" placeholder="选择省" v-model="form.area.split(',')[0]" readonly>
                            <input type="text" placeholder="选择市" v-model="form.area.split(',')[1]" readonly>
                            <input type="text" placeholder="选择区" v-model="form.area.split(',')[2]" readonly>
                        </div>
                    </div>
                    <div class="form-item">
                        <label>街道地址:</label>
                        <input type="text" v-model="form.address" @keyup.enter="submit">
                    </div>
                    <div class="form-footer">
                        <p class="fz1">◎</p>
                        <p>免责声明:<br>本活动专题收集的所有信息仅供发放活动奖品使用，在未经得本人同意情况下绝对不会将您的任何资料以任何方式泄露给第三方。由于您自身原因如共享登录账号等导致的个人信息披露，活动方概不负责。
                        </p>
                    </div>
                    <img src="img/button6.png" class="back2 animate__animated animate__fadeInUp" @click="submit">
                </form>
                <van-popup v-model:show="popupShow" round position="bottom">
                    <van-picker show-toolbar title="请选择地区" :columns="options" default-index="11"
                        v-model="selectedValues" @cancel="popupShow = false" @confirm="onConfirm"></van-picker>
                </van-popup>
            </div>
        </div>
        <!-- 游戏页 -->
        <div class="page bj2 fc" :class="{blur:show}" v-if="page===5">
            <div class="game_area">
                <div class="time">60s</div>
                <div class="level">第一关</div>
                <canvas ref="gameCanvas" width="750" height="1334"></canvas>
            </div>
        </div>
        <!-- 提交成功弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button4_2.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
        <!-- 无机会弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <img src="img/close.png" class="close2" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    
    <!-- <script src="https://ztimg.hefei.cc/static/common/js/libs/vue.js"></script> -->
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick } = Vue
    </script>
    <script src="game.js"></script>
    <script>
        const foodList = [
            {name:'香菇',src:'img/food1.png',synthetic:'img/food1_synthetic.png',width:160,syntheticWidht:160},
            {name:'鸡翅',src:'img/food2.png',synthetic:'img/food2_synthetic.png',width:160,syntheticWidht:160},
            {name:'海带',src:'img/food3.png',synthetic:'img/food3_synthetic.png',width:160,syntheticWidht:160},
            {name:'龙虾',src:'img/food4.png',synthetic:'img/food4_synthetic.png',width:160,syntheticWidht:160},
            {name:'鱿鱼',src:'img/food5.png',synthetic:'img/food5_synthetic.png',width:160,syntheticWidht:160},
            {name:'烤肠',src:'img/food6.png',synthetic:'img/food6_synthetic.png',width:160,syntheticWidht:160},
            {name:'玉米',src:'img/food7.png',synthetic:'img/food7_synthetic.png',width:160,syntheticWidht:160},
            {name:'烤肉',src:'img/food8.png',synthetic:'img/food8_synthetic.png',width:160,syntheticWidht:160},
        ]
        let rotation = [];
        window.startData = {
            rotation:rotation,
            jihui: '5',
            prize: '{$prize}',
            ad: '{$ad}',
            prizeTime: '{$prizeTime}',
            userInfo: {
                name: '{$name}',
                phone: '{$phone}',
                area: '{$area}',
                address: '{$address}',
            },
            nickname: '{$nickname}',
            avatar: '{$headimgurl}',
            wlq:'{$wlq}',
            endtime: '{$endtime}',
            writeFlag: '{$writeFlag}',
            rule: `活动时间：2025年7月31日—2025年8月3日<br>
            活动规则：<br>
            1、点击开始进入游戏界面，倒计时3秒后，屏幕上会出现串有不同食材的烤串以及空竹签，选择相同种类的食材放入同一根竹签中，注意每次仅可选择竹签最上方一种食材（可以是多个相连的同种食材）进行移动。当4个相同的食材全部串至一根竹签上时，即视为合成成功，完成屏幕中所有食材的合成，便可成功通关并进入下一关。<br>
            2、本次游戏共分为三关，60S内全部完成即可获得抽奖机会。每个用户每日有5次挑战机会，中途退出视为消耗1次机会。<br>
            3、在挑战成功的用户中随机抽选300名，派送礼品一份，奖品多多快来参与~<br>
            4、用户在填写信息时，需填写详细联系方式（姓名、电话号码和邮寄地址），因个人原因（包括但不限于地址电话填错、电话无法接通、地址填写不完整等）导致快递退回，均不予补发处理。<br>
            5、在同一次活动中，姓名、手机号、收货地址中有一项及以上相同时，默认只寄发首次中奖奖品。物流相关事宜（包括签收、破损、丢件等问题），由中奖者自行和快递公司协商处理。<br>
            6、活动过程中有任何问题可发送疑问至“甜润世界”微信公众号后台。`,
        }

        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('bgm.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const handleRotation = startData.rotation.map(item => item.split(',')) // 设置顶部获奖数据
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 5
                        opportunity.value--
                        defaultHttp('gamestart', { status: 1 })
                    } else {
                        show.value = 2
                    }
                }

                const edit = () => {
                    if (writeFlag === 0) {
                        return vantAlert('活动已结束');
                    } else {
                        goForm()
                    }
                }
                const goForm = () => {
                    page.value = 4
                    show.value = 0
                }

                // 登记信息功能
                const { form, popupShow, options, focus, onConfirm, check, selectedValues } = createdForm()
                form.value = userInfo
                const submit = throttle(async () => {
                    if (!check()) return
                    const res = await defaultHttp('action', Object.assign({ act: 'sub' }, form.value), { status: 1 })
                    if (res.status == 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg)
                    }
                })
                // 刷新页面功能
                const { reload, savePage } = useReload()
                if (savePage) { page.value = +savePage }
                // 判断是否是初次进入网页,执行预加载
                const { loadStart, progress, progressShow, startFlag } = cheackStartPopup([])
                loadStart()
                if(startFlag){
                    page.value = 2
                }
                // 检查未领取
                if (startData.wlq === '1') {
                    vant.showConfirmDialog({
                        title: '温馨提示',
                        message: '您已中奖，请尽快完善领奖信息！',
                        confirmButtonText: '去领取',
                        cancelButtonText: '不领取'
                    }).then(() => {
                        show.value = 0
                        page.value = 4
                    })
                }

                const gameEnd = throttle(async () => {
                    const res = await defaultHttp('gameEnd', { cg:gameState.value.score>=gameConfig.targetScore?1:0, timer: gameConfig.gameTime-gameState.value.timeLeft, score:gameState.value.score }, { status: 1 })
                    if (res.status === 1) {
                        show.value = 3
                    } else if(res.status === 2) {
                        show.value = 4
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '扑克牌一份', ad: '云水雅玩礼', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 5
                        prizeData.value = res.data
                    } else if (res.status === 2) {
                        show.value = 6 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })

                // 游戏逻辑
                var pop1 = new Howl({
                    src:'pop1.mp3',
                    preload: true,
                })
                var pop2 = new Howl({
                    src:'pop2.mp3',
                    preload: true,
                })

                // 烤串游戏
                const gameCanvas = ref(null)
                let kaoGame = null

                const initKaoGame = async () => {
                    if (gameCanvas.value && typeof useKaoGame === 'function') {
                        kaoGame = useKaoGame(gameCanvas, (isWin) => {
                            console.log('游戏结束，是否胜利:', isWin)
                            // 游戏结束处理
                            if (isWin) {
                                // 胜利逻辑
                                vantAlert('恭喜通关！')
                            } else {
                                // 失败逻辑
                                vantAlert('时间到！挑战失败')
                            }
                            // 返回首页
                            setTimeout(() => {
                                page.value = 1
                            }, 2000)
                        })

                        await kaoGame.initGame()
                        kaoGame.startGame()
                    }
                }

                // 监听页面变化，当进入游戏页面时初始化游戏
                watch(page, async (newPage) => {
                    if (newPage === 5) {
                        await nextTick()
                        initKaoGame()
                    } else if (kaoGame) {
                        // 离开游戏页面时清理资源
                        kaoGame.removeEvents()
                    }
                })

                return {
                    startData, page, show,
                    handleRotation, edit, goForm,
                    on, bgClick,
                    start, submit, reload,
                    form, popupShow, options, focus, onConfirm, check, selectedValues,
                    prizeData, getPrize,
                    gameCanvas
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
        <!--分享-->
{include file="share"/}
</body>

</html>



