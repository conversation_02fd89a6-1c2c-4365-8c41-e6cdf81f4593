const fs = require('fs');
const path = require("path")
// 读取 CSS 文件内容
const css = fs.readFileSync(path.join(__dirname,'index.css'), 'utf8');
// 获取命令行参数
const paramName = '-r=';
const ratioArg = process.argv.find(arg => arg.startsWith(paramName));
const ratio = parseFloat(ratioArg ? ratioArg.substring(paramName.length) : '400');

// 匹配所有的vw单位
const vwRegExp = /([\d.]+)vw/g;
let match;
let result = css;

while ((match = vwRegExp.exec(css)) !== null) {
  // 计算px值
  const vwValue = parseFloat(match[1]);
  const pxValue = Math.round(vwValue * (ratio)/667);

  // 替换vw单位为px单位
  result = result.replace(`${vwValue}vw`, `${pxValue}vh`);
}
let polyfill = `
var styleTag = document.createElement('link');
styleTag.id = 'custom-styles';
styleTag.rel = 'stylesheet';
styleTag.href = \`css/vwtopx.css?v=\${Math.floor(Math.random()*10000)}\`;
function handleAspectRatio() {
  var screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  var screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  var aspectRatio = screenWidth / screenHeight;
  var pagestyle = document.querySelector('link#custom-styles')
  if (aspectRatio > ${ratio/667}) {
    if(!pagestyle){
      document.head.appendChild(styleTag)
    }
  } else {
    if(pagestyle){
      pagestyle.parentNode.removeChild(pagestyle)
    }
  }
  
}
window.addEventListener('load', handleAspectRatio);
window.addEventListener('resize', e=>{
  if(!["input","textarea"].includes(document.activeElement.tagName.toLowerCase())){
    handleAspectRatio()
  }
});
`
// 输出转换后的CSS内容
// console.log(result);
fs.writeFile(path.join(__dirname,'vwtopx.css'), result,()=>{})
fs.writeFile(path.join(__dirname,'polyfill.js'), polyfill,()=>{})

//调用方式
// node .\css\vwtopx.js -r=750