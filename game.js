// 烤串合成游戏
function useKaoGame(canvasRef, onGameEnd) {
    // 食物列表
    const foodList = [
        {
            id: 0,
            name: '香菇',
            src: 'img/food1.png',
            synthetic: 'img/food1_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 1,
            name: '鸡翅',
            src: 'img/food2.png',
            synthetic: 'img/food2_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 2,
            name: '海带',
            src: 'img/food3.png',
            synthetic: 'img/food3_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 3,
            name: '龙虾',
            src: 'img/food4.png',
            synthetic: 'img/food4_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 4,
            name: '鱿鱼',
            src: 'img/food5.png',
            synthetic: 'img/food5_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 5,
            name: '烤肠',
            src: 'img/food6.png',
            synthetic: 'img/food6_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 6,
            name: '玉米',
            src: 'img/food7.png',
            synthetic: 'img/food7_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        },
        {
            id: 7,
            name: '烤肉',
            src: 'img/food8.png',
            synthetic: 'img/food8_synthetic.png',
            width: 160,
            syntheticWidth: 160,
            img: null,
            syntheticImg: null,
            aspectRatio: 1,
            syntheticAspectRatio: 1
        }
    ];

    // 游戏配置
    const config = {
        gameTime: 60,           // 游戏时间
        targetScore: 100,       // 目标分数
        canvasWidth: 750,       // 画布宽度
        canvasHeight: 1334,     // 画布高度
        topSpaceRatio: 0.1,     // 上部空间占比20%
        stickWidth: 16,         // 木签宽度
        stickHeight: 485,       // 木签高度
        maxFoodsPerStick: 4,    // 每根木签最多食物数量
        animationDuration: 300, // 动画持续时间
        selectOffset: 15,       // 选中时向上偏移距离
    };

    // 动态计算木签位置
    const calculateStickPositions = () => {
        const gameAreaHeight = config.canvasHeight * (1 - config.topSpaceRatio); // 游戏可用区域高度
        const gameAreaTop = config.canvasHeight * config.topSpaceRatio; // 游戏区域起始Y坐标

        // 将游戏区域分为上下两部分
        const topSectionHeight = gameAreaHeight / 2;
        const bottomSectionHeight = gameAreaHeight / 2;

        // 计算各部分的中心Y坐标
        const topSectionCenterY = gameAreaTop + topSectionHeight / 2;
        const bottomSectionCenterY = gameAreaTop + topSectionHeight + bottomSectionHeight / 2;

        return {
            top: [
                { x: config.canvasWidth * 0.33, y: topSectionCenterY },  // 上部分第一根木签 (1/3位置)
                { x: config.canvasWidth * 0.67, y: topSectionCenterY }   // 上部分第二根木签 (2/3位置)
            ],
            bottom: [
                { x: config.canvasWidth * 0.5, y: bottomSectionCenterY }  // 下部分木签 (中心位置)
            ]
        };
    };

    // 游戏状态
    const gameState = ref({
        score: 0,
        timeLeft: config.gameTime,
        isRunning: false,
        isGameOver: false,
        selectedFoods: null,     // 选中的食物信息 {stickIndex, foodIndex, count}
        sticks: [],              // 木签状态
        animations: [],          // 动画列表
        successAnimations: []    // 成功动画列表
    });

    // 音效
    const successSound = new Howl({
        src: ['./pop1.mp3'],
        volume: 0.5,
        html5: true,
        preload: true
    });

    // 图片资源
    const images = {
        stick: null,
        light: null
    };

    let ctx = null;
    let animationFrame = null;
    let timerInterval = null;

    // 加载图片资源
    const loadAssets = () => {
        return new Promise((resolve) => {
            let totalImages = foodList.length * 2 + 2; // 食物图片 + 合成图片 + 木签 + 光效
            let loadedImages = 0;

            console.log('开始加载图片资源...');
            console.log(`需要加载的图片总数: ${totalImages}`);

            const onLoad = () => {
                loadedImages++;
                console.log(`已加载图片: ${loadedImages}/${totalImages}`);
                if (loadedImages === totalImages) {
                    console.log('所有图片加载完成');
                    // 计算所有图片的宽高比
                    foodList.forEach((food) => {
                        if (food.img) {
                            food.aspectRatio = food.img.height / food.img.width;
                        }
                        if (food.syntheticImg) {
                            food.syntheticAspectRatio = food.syntheticImg.height / food.syntheticImg.width;
                        }
                    });
                    resolve();
                }
            };

            // 加载食物图片
            foodList.forEach((food) => {
                // 加载普通食物图片
                const img = new Image();
                img.onload = onLoad;
                img.onerror = (e) => {
                    console.error('加载食物图片失败:', food.src, e);
                    onLoad(); // 即使失败也要继续
                };
                console.log(`开始加载食物图片: ${food.src}`);
                img.src = food.src;
                food.img = img;

                // 加载合成食物图片
                const syntheticImg = new Image();
                syntheticImg.onload = onLoad;
                syntheticImg.onerror = (e) => {
                    console.error('加载合成食物图片失败:', food.synthetic, e);
                    onLoad(); // 即使失败也要继续
                };
                console.log(`开始加载合成食物图片: ${food.synthetic}`);
                syntheticImg.src = food.synthetic;
                food.syntheticImg = syntheticImg;
            });

            // 加载木签图片
            images.stick = new Image();
            images.stick.onload = onLoad;
            images.stick.onerror = (e) => {
                console.error('加载木签图片失败:', 'img/qian.png', e);
                onLoad();
            };
            console.log('开始加载木签图片: img/qian.png');
            images.stick.src = 'img/qian.png';

            // 加载光效图片
            images.light = new Image();
            images.light.onload = onLoad;
            images.light.onerror = (e) => {
                console.error('加载光效图片失败:', 'img/light.png', e);
                onLoad();
            };
            console.log('开始加载光效图片: img/light.png');
            images.light.src = 'img/light.png';
        });
    };

    // 初始化木签状态 - 第一关
    const initSticks = () => {
        const sticks = [];
        const stickPositions = calculateStickPositions(); // 动态计算位置

        // 上部分2根木签，每根4个食物
        stickPositions.top.forEach((pos, index) => {
            const foods = [];
            // 第一关只使用前2种食物：香菇(0)和鸡翅(1)
            const foodType = index; // 第一根木签放香菇，第二根放鸡翅
            for (let i = 0; i < 4; i++) {
                foods.push({
                    type: foodType,
                    isSynthetic: false
                });
            }
            sticks.push({
                x: pos.x,
                y: pos.y,
                foods: foods,
                isSelected: false
            });
        });

        // 下部分1根空木签
        stickPositions.bottom.forEach((pos) => {
            sticks.push({
                x: pos.x,
                y: pos.y,
                foods: [],
                isSelected: false
            });
        });

        gameState.value.sticks = sticks;
    };

    // 获取木签上最上面的连续相同食物
    const getTopSameFoods = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];
        if (stick.foods.length === 0) return { count: 0, type: null };

        const topFood = stick.foods[stick.foods.length - 1];
        let count = 1;

        // 从最上面开始向下查找相同类型的连续食物
        for (let i = stick.foods.length - 2; i >= 0; i--) {
            if (stick.foods[i].type === topFood.type &&
                stick.foods[i].isSynthetic === topFood.isSynthetic) {
                count++;
            } else {
                break;
            }
        }

        return { count, type: topFood.type, isSynthetic: topFood.isSynthetic };
    };

    // 检查是否可以移动到目标木签
    const canMoveTo = (targetStickIndex, foodCount) => {
        const targetStick = gameState.value.sticks[targetStickIndex];
        const remainingSpace = config.maxFoodsPerStick - targetStick.foods.length;

        // 如果是合成食物，只能放1个，且木签必须为空
        if (foodCount === 1 && gameState.value.selectedFoods?.isSynthetic) {
            return targetStick.foods.length === 0;
        }

        return remainingSpace >= foodCount;
    };

    // 检查是否可以合成
    const checkSynthesis = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];
        if (stick.foods.length !== 4) return false;

        // 检查是否都是相同类型的普通食物
        const firstFood = stick.foods[0];
        if (firstFood.isSynthetic) return false;

        return stick.foods.every(food =>
            food.type === firstFood.type && !food.isSynthetic
        );
    };

    // 执行合成
    const performSynthesis = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];

        // 播放成功音效
        successSound.play();

        // 添加成功动画
        gameState.value.successAnimations.push({
            x: stick.x,
            y: stick.y - 100,
            startTime: Date.now(),
            scale: 1
        });

        // 替换为合成食物
        const foodType = stick.foods[0].type;
        stick.foods = [{
            type: foodType,
            isSynthetic: true
        }];

        // 增加分数
        gameState.value.score += 10;
    };

    // 移动食物
    const moveFoods = (fromStickIndex, toStickIndex, count) => {
        const fromStick = gameState.value.sticks[fromStickIndex];
        const toStick = gameState.value.sticks[toStickIndex];

        // 移除选中的食物
        const movedFoods = fromStick.foods.splice(-count, count);

        // 添加到目标木签
        toStick.foods.push(...movedFoods);

        // 检查目标木签是否可以合成
        if (checkSynthesis(toStickIndex)) {
            setTimeout(() => {
                performSynthesis(toStickIndex);
            }, 100);
        }
    };

    // 绘制木签
    const drawStick = (stick, stickIndex) => {
        // 绘制木签
        if (images.stick && images.stick.complete) {
            ctx.drawImage(images.stick,
                stick.x - config.stickWidth / 2,
                stick.y - config.stickHeight / 2,
                config.stickWidth,
                config.stickHeight
            );
        }

        // 绘制食物
        stick.foods.forEach((food, foodIndex) => {
            const foodData = foodList[food.type];
            const img = food.isSynthetic ? foodData.syntheticImg : foodData.img;
            const aspectRatio = food.isSynthetic ? foodData.syntheticAspectRatio : foodData.aspectRatio;
            const foodWidth = food.isSynthetic ? foodData.syntheticWidth : foodData.width;

            if (img && img.complete) {
                const foodHeight = foodWidth * aspectRatio;
                // 食物从木签底部开始向上排列
                let foodY = stick.y + config.stickHeight / 2 - (foodIndex + 1) * (foodHeight + 5);

                // 如果是选中的食物，向上偏移
                const selectedFoods = gameState.value.selectedFoods;
                if (selectedFoods && selectedFoods.stickIndex === stickIndex) {
                    const selectedStartIndex = stick.foods.length - selectedFoods.count;
                    if (foodIndex >= selectedStartIndex) {
                        foodY -= config.selectOffset;

                        // 绘制光效背景
                        if (images.light && images.light.complete) {
                            ctx.drawImage(images.light,
                                stick.x - foodWidth / 2 - 10,
                                foodY - 10,
                                foodWidth + 20,
                                foodHeight + 20
                            );
                        }
                    }
                }

                ctx.drawImage(img,
                    stick.x - foodWidth / 2,
                    foodY,
                    foodWidth,
                    foodHeight
                );
            }
        });
    };

    // 绘制成功动画
    const drawSuccessAnimations = () => {
        gameState.value.successAnimations.forEach((anim, index) => {
            const progress = (Date.now() - anim.startTime) / 1000; // 1秒动画
            if (progress >= 1) {
                gameState.value.successAnimations.splice(index, 1);
                return;
            }

            const scale = 1 + Math.sin(progress * Math.PI) * 0.5; // 放大缩小效果
            const alpha = 1 - progress;

            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.translate(anim.x, anim.y);
            ctx.scale(scale, scale);

            // 绘制成功文字
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 40px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('合成成功!', 0, 0);

            ctx.restore();
        });
    };

    // 主绘制函数
    const draw = () => {
        if (!ctx) return;

        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);

        // 绘制所有木签
        gameState.value.sticks.forEach((stick, index) => {
            drawStick(stick, index);
        });

        // 绘制成功动画
        drawSuccessAnimations();
    };

    // 游戏主循环
    const gameLoop = () => {
        if (!gameState.value.isRunning) return;

        draw();
        animationFrame = requestAnimationFrame(gameLoop);
    };

    // 开始计时器
    const startTimer = () => {
        if (timerInterval) clearInterval(timerInterval);

        timerInterval = setInterval(() => {
            if (gameState.value.timeLeft > 0 && gameState.value.isRunning) {
                gameState.value.timeLeft--;

                if (gameState.value.timeLeft <= 0) {
                    endGame();
                }
            }
        }, 1000);
    };

    // 点击检测
    const checkClick = (x, y) => {
        // 检查点击的是哪根木签
        for (let stickIndex = 0; stickIndex < gameState.value.sticks.length; stickIndex++) {
            const stick = gameState.value.sticks[stickIndex];

            // 扩大点击区域，使用食物宽度作为点击区域宽度
            const clickWidth = Math.max(160, 120); // 使用食物的标准宽度160，最小120像素
            const stickLeft = stick.x - clickWidth / 2;
            const stickRight = stick.x + clickWidth / 2;
            const stickTop = stick.y - config.stickHeight / 2;
            const stickBottom = stick.y + config.stickHeight / 2;

            if (x >= stickLeft && x <= stickRight && y >= stickTop && y <= stickBottom) {
                handleStickClick(stickIndex);
                break;
            }
        }
    };

    // 处理木签点击
    const handleStickClick = (stickIndex) => {
        const stick = gameState.value.sticks[stickIndex];

        if (gameState.value.selectedFoods) {
            // 已有选中的食物，尝试移动
            const selectedFoods = gameState.value.selectedFoods;

            if (selectedFoods.stickIndex === stickIndex) {
                // 点击同一根木签，取消选中
                gameState.value.selectedFoods = null;
            } else {
                // 点击不同木签，尝试移动
                if (canMoveTo(stickIndex, selectedFoods.count)) {
                    moveFoods(selectedFoods.stickIndex, stickIndex, selectedFoods.count);
                    gameState.value.selectedFoods = null;
                } else {
                    // 无法移动，取消选中
                    gameState.value.selectedFoods = null;
                }
            }
        } else {
            // 没有选中的食物，尝试选中当前木签最上面的食物
            if (stick.foods.length > 0) {
                const topFoods = getTopSameFoods(stickIndex);
                if (topFoods.count > 0) {
                    gameState.value.selectedFoods = {
                        stickIndex: stickIndex,
                        count: topFoods.count,
                        type: topFoods.type,
                        isSynthetic: topFoods.isSynthetic
                    };
                }
            }
        }
    };

    // 事件处理
    const handleClick = (e) => {
        if (!gameState.value.isRunning) return;

        if (e.type === 'touchstart') {
            e.preventDefault();
        }

        const point = e.touches ? e.touches[0] : e;
        const rect = canvasRef.value.getBoundingClientRect();
        const scaleX = canvasRef.value.width / rect.width;
        const scaleY = canvasRef.value.height / rect.height;
        const x = (point.clientX - rect.left) * scaleX;
        const y = (point.clientY - rect.top) * scaleY;

        checkClick(x, y);
    };

    // 添加事件监听
    const addEvents = () => {
        const canvas = canvasRef.value;
        canvas.addEventListener('mousedown', handleClick);
        canvas.addEventListener('touchstart', handleClick, { passive: false });

        // 阻止触摸时的页面滚动
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
        }, { passive: false });
    };

    // 移除事件监听
    const removeEvents = () => {
        const canvas = canvasRef.value;
        canvas.removeEventListener('mousedown', handleClick);
        canvas.removeEventListener('touchstart', handleClick);

        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        successSound.stop();
    };

    // 检查游戏是否胜利
    const checkWin = () => {
        // 检查是否所有食物都已合成
        let totalSyntheticFoods = 0;
        gameState.value.sticks.forEach(stick => {
            stick.foods.forEach(food => {
                if (food.isSynthetic) {
                    totalSyntheticFoods++;
                }
            });
        });

        // 第一关需要合成2个食物（每种食物1个）
        return totalSyntheticFoods >= 2;
    };

    // 初始化游戏
    const initGame = async () => {
        if (!canvasRef.value) return;

        try {
            ctx = canvasRef.value.getContext('2d');
            await loadAssets();
            initSticks();
            addEvents();
            console.log('游戏初始化完成');
        } catch (error) {
            console.error('游戏初始化失败:', error);
        }
    };

    // 开始游戏
    const startGame = () => {
        // 确保所有图片都已加载
        const allImagesLoaded = foodList.every(food =>
            food.img && food.img.complete &&
            food.syntheticImg && food.syntheticImg.complete
        ) && images.stick && images.stick.complete &&
        images.light && images.light.complete;

        if (!allImagesLoaded) {
            console.error('图片资源未完全加载，无法开始游戏');
            return;
        }

        gameState.value = {
            score: 0,
            timeLeft: config.gameTime,
            isRunning: true,
            isGameOver: false,
            selectedFoods: null,
            sticks: gameState.value.sticks, // 保持已初始化的木签状态
            animations: [],
            successAnimations: []
        };

        startTimer();
        gameLoop();
    };

    // 结束游戏
    const endGame = () => {
        gameState.value.isRunning = false;
        gameState.value.isGameOver = true;

        if (timerInterval) {
            clearInterval(timerInterval);
        }

        const isWin = checkWin();
        if (onGameEnd) {
            onGameEnd(isWin);
        }
    };

    return {
        gameState,
        initGame,
        startGame,
        endGame,
        addEvents,
        removeEvents,
        checkWin
    };
}
