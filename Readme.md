参考game2.js帮我写一个烤串合成游戏game.js，foodList也写到js里面去。
首先是图片加载逻辑，一定要参考game2.js，合成前和合成后的图片要分别读取图片宽高比存到foodList，绘制食物的时候要用到foodList里的宽度和宽高比。
然后是挂载事件，也要参考game2.js，兼容mouse和touch。
游戏总体布局是屏幕分上下部分，上下部分拥有木签，木签使用img/qian.png
你先完成游戏第一关，第一关使用2种食物，上部分2根木签，下部分1根木签，上部分每根木签串有4串食物（一共是8个食物，要保证每种食物为4个），下部分的木签上是空的。注意每个木签最多存放4个普通食物或者1个合成后的食物，可以用手点击选中木签最上面的食物（如果是同种食物连续的会被一起选中），选中的效果为食物后面加一个背景光img/light.png同时向上偏移一段距离，选中效果要有一个渐变动画。选中食物之后可以点击别的木签，如果木签有剩余空间，选中的食物就可以移动过去，然后执行移除选中效果的动画。当4个相同的食物移动到同一个木签上时，就会自动合成一个新食物，新食物的图片在foodList的synthetic字段中，注意新食物要有一个生成的动画（如放大、旋转等），并且这个新食物要替代原来4个食物的位置，也就是这个新食物要和原来4个食物处在同一位置，新食物生成后，要执行一个成功的声音效果，这个声音效果在下面的代码中。
