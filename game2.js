function useGame(canvasRef, onGameEnd) {
    // 游戏配置
    const config = {
        timeLimit: 30,          // 游戏时间限制（秒）
        winScore: 100,         // 胜利需要的分数
        successScore: 15,      // 成功合成粽子得分
        failScore: -5,         // 错误选择扣分
        itemSize: 80,          // 食材大小
        itemHeights: {         // 存储不同物品的高度
            food: new Array(7).fill(0),    // 初始化7个食材的高度数组
            zongzi: new Array(3).fill(0)   // 初始化3个粽子的高度数组
        },
        moveSpeed: 300,          // 食材移动速度（像素/秒）
        spawnInterval: 400,   // 生成食材间隔
        plateSize: 176,        // 盘子宽度
        foodOffsetY: -20,      // 食材相对于盘子中心的垂直偏移
        animationDuration: 500,    // 动画持续时间（毫秒）
        safeSpawnArea: {       // 安全生成区域
            top: 100,          // 顶部安全距离
            margin: 60         // 左右边距
        },
        platePositions: [      // 三个盘子的位置
            {x: 111, y: 881},
            {x: 328, y: 881},
            {x: 536, y: 881}
        ],
        zongziDisplay: {       // 显示当前和下一个粽子的位置
            current: {
                x: 248,
                y: 630,
                size: 150      // 当前粽子显示大小
            },
            next: {
                x: 30,
                y: 660,
                size: 120       // 下一个粽子显示大小
            }
        },
        foodNameDisplay: {     // 食材名称显示配置
            fontSize: 30,      // 字体大小
            fontFamily: 'Arial', // 字体
            offsetY: 50,      // 相对于盘子中心的垂直偏移
            colors: {
                normal: '#fff',  // 未收集时的颜色
                collected: '#fff' // 已收集时的颜色
            }
        },
        errorDisplay: {        // 错误提示显示配置
            fontSize: 50,      // 扣分提示字号
            fontFamily: 'Arial', // 字体
            fontWeight: 'bold',  // 字体粗细
            offsetY: 50        // 向上移动的距离（像素）
        },
        collectedDisplay: {    // 已收集提示显示配置
            fontSize: 40,      // 已收集提示字号
            fontFamily: 'Arial', // 字体
            fontWeight: 'bold',  // 字体粗细
            offsetY: 50,       // 向上移动的距离（像素）
            color: '#FFFFFF',  // 已收集提示颜色（白色）
            strokeColor: '#2f7d55', // 描边颜色（绿色）
            strokeWidth: 8     // 描边宽度
        },
        successDisplay: {      // 成功提示显示配置
            fontSize: 60,      // 加分提示字号
            fontFamily: 'Arial', // 字体
            fontWeight: 'bold',  // 字体粗细
            offsetY: 100,      // 向上移动的距离（像素）
            color: '#00ff00'   // 成功提示颜色
        }
    }

    // 游戏状态
    const gameState = ref({
        score: 0,
        timer: config.timeLimit,
        isRunning: false,
        isGameOver: false,
        currentZongzi: 1,      // 当前需要制作的粽子类型（1-3）
        nextZongzi: 1,         // 下一个要制作的粽子类型
        collectedItems: [],    // 已收集的食材
        animatingItems: [],    // 正在进行动画的食材
        errorAnimations: [],   // 错误提示动画
        successAnimations: [], // 成功提示动画
        collectedAnimations: [], // 已收集提示动画
        useSecondSet: false,   // 是否使用第二组食材
    })

    // 音效
    const collectSound = new Howl({
        src: ['./pop.mp3'],
        volume: 0.5,
        html5: true,
        preload: true,
        pool: 10
    })
    // 资源加载
    const images = {
        zongzi: [],    // zongzi1-zongzi3的图片
        zongzi2: [],   // zongzi4-zongzi6的图片
        plate: null     // 盘子图片
    }

    // 添加食材类型定义
    const foodTypes = [
        { id: 0, name: '红茶', image: 'food1.png', forZongzi: 1, img: null },
        { id: 1, name: '红枣', image: 'food2.png', forZongzi: 1, img: null },
        { id: 2, name: '黑松露', image: 'food3.png', forZongzi: 2, img: null },
        { id: 3, name: '火腿', image: 'food4.png', forZongzi: 2, img: null },
        { id: 4, name: '蛋黄', image: 'food5.png', forZongzi: 3, img: null },
        { id: 5, name: '咸肉', image: 'food6.png', forZongzi: 3, img: null },
        { id: 6, name: '糯米', image: 'food7.png', forZongzi: [1,2,3], img: null }
    ]

    const foodTypes2 = [
        { id: 0, name: '鲍鱼', image: 'food11.png', forZongzi: 4, img: null },
        { id: 1, name: '海参', image: 'food12.png', forZongzi: 4, img: null },
        { id: 2, name: '蜂蜜', image: 'food13.png', forZongzi: 5, img: null },
        { id: 3, name: '红枣', image: 'food14.png', forZongzi: 5, img: null },
        { id: 4, name: '石斛茶', image: 'food15.png', forZongzi: 6, img: null },
        { id: 5, name: '红豆', image: 'food16.png', forZongzi: 6, img: null },
        { id: 6, name: '糯米', image: 'food17.png', forZongzi: [4,5,6], img: null }
    ]

    // 修改加载图片资源的逻辑
    const loadAssets = (useSecondSet = false) => {
        return new Promise((resolve) => {
            const currentFoodTypes = useSecondSet ? foodTypes2 : foodTypes
            let totalImages = currentFoodTypes.length + 3 + 3 + 1  // 所有食材 + 3个粽子 + 3个第二组粽子 + 1个盘子
            let loadedImages = 0

            console.log('开始加载图片资源...')
            console.log(`需要加载的图片总数: ${totalImages}`)
            console.log(`当前使用食材组: ${useSecondSet ? '第二组' : '第一组'}`)

            const onLoad = () => {
                loadedImages++
                console.log(`已加载图片: ${loadedImages}/${totalImages}`)
                if (loadedImages === totalImages) {
                    console.log('所有图片加载完成')
                    // 计算所有图片的显示高度
                    currentFoodTypes.forEach((food, index) => {
                        const aspectRatio = food.img.height / food.img.width
                        config.itemHeights.food[index] = config.itemSize * aspectRatio
                    })
                    for (let i = 0; i < 3; i++) {
                        const aspectRatio = images.zongzi[i].height / images.zongzi[i].width
                        config.itemHeights.zongzi[i] = config.itemSize * aspectRatio
                    }
                    resolve()
                }
            }

            // 加载食材图片
            currentFoodTypes.forEach((food) => {
                const img = new Image()
                img.onload = onLoad
                img.onerror = (e) => {
                    console.error('加载图片失败:', food.image, e)
                }
                console.log(`开始加载食材图片: ${food.image}`)
                img.src = `img/${food.image}`
                food.img = img
            })

            // 加载第一组粽子图片
            for (let i = 1; i <= 3; i++) {
                const img = new Image()
                img.onload = onLoad
                img.onerror = (e) => {
                    console.error('加载粽子图片失败:', `zongzi${i}.png`, e)
                }
                console.log(`开始加载粽子图片: zongzi${i}.png`)
                img.src = `img/zongzi${i}.png`
                images.zongzi[i-1] = img
            }

            // 加载第二组粽子图片
            for (let i = 4; i <= 6; i++) {
                const img = new Image()
                img.onload = onLoad
                img.onerror = (e) => {
                    console.error('加载粽子图片失败:', `zongzi${i}.png`, e)
                }
                console.log(`开始加载粽子图片: zongzi${i}.png`)
                img.src = `img/zongzi${i}.png`
                images.zongzi2[i-4] = img
            }

            // 加载盘子图片
            images.plate = new Image()
            images.plate.onload = onLoad
            images.plate.onerror = (e) => {
                console.error('加载盘子图片失败:', 'panzi.png', e)
            }
            console.log('开始加载盘子图片: panzi.png')
            images.plate.src = 'img/panzi.png'
        })
    }

    // 游戏元素
    let items = []             // 移动中的食材
    let ctx = null
    let animationFrame = null
    let timerInterval = null
    let lastSpawnTime = 0
    let lastUpdateTime = 0     // 添加新的时间变量用于计算移动deltaTime

    // 创建新食材
    const createItem = () => {
        // 获取当前粽子需要的食材
        const requiredFoods = getRequiredFoods(gameState.value.currentZongzi, gameState.value.useSecondSet)
        
        // 70%概率生成当前粽子需要的食材
        if (Math.random() < 0.7) {
            // 从当前粽子需要的食材中随机选择一个
            const randomIndex = Math.floor(Math.random() * requiredFoods.length)
            const foodIndex = requiredFoods[randomIndex]
            return {
                x: canvasRef.value.width,  // 从右侧出现
                y: Math.random() * (canvasRef.value.height / 3) + config.safeSpawnArea.top,
                type: foodIndex,
                width: config.itemSize,
                height: config.itemHeights.food[foodIndex]
            }
        } else {
            // 30%概率随机生成其他食材
            const foodIndex = Math.floor(Math.random() * 7)  // 随机选择食材（0-6）
            return {
                x: canvasRef.value.width,
                y: Math.random() * (canvasRef.value.height / 3) + config.safeSpawnArea.top,
                type: foodIndex,
                width: config.itemSize,
                height: config.itemHeights.food[foodIndex]
            }
        }
    }

    // 检查是否可以合成粽子
    const checkZongziCompletion = () => {
        const items = gameState.value.collectedItems
        if (items.length < 3) return false

        // 获取当前使用的食材数组
        const currentFoodTypes = gameState.value.useSecondSet ? foodTypes2 : foodTypes
        const currentZongzi = gameState.value.currentZongzi

        // 获取当前粽子需要的食材
        const requiredFoods = getRequiredFoods(currentZongzi, gameState.value.useSecondSet)
        
        // 检查是否收集了所有必需的食材
        const hasAllRequiredFoods = requiredFoods.every(requiredFoodIndex => 
            items.includes(requiredFoodIndex)
        )

        // 检查是否只收集了必需的食材（没有多余的食材）
        const hasOnlyRequiredFoods = items.every(itemType => 
            requiredFoods.includes(itemType)
        )

        return hasAllRequiredFoods && hasOnlyRequiredFoods
    }

    // 获取当前使用的食材数组
    const getCurrentFoodTypes = () => {
        return gameState.value.useSecondSet ? foodTypes2 : foodTypes
    }

    // 获取当前粽子需要的食材
    const getRequiredFoods = (zongziType, useSecondSet = false) => {
        const currentFoodTypes = useSecondSet ? foodTypes2 : foodTypes
        return currentFoodTypes
            .map((food, index) => ({ ...food, index }))
            .filter(food => {
                if (Array.isArray(food.forZongzi)) {
                    return food.forZongzi.includes(zongziType)
                }
                return food.forZongzi === zongziType
            })
            .map(food => food.index)
    }

    // 更新物品位置
    const updateItems = () => {
        const now = Date.now()
        const deltaTime = (now - lastUpdateTime) / 1000  // 使用lastUpdateTime计算移动deltaTime
        lastUpdateTime = now  // 更新lastUpdateTime

        // 生成新食材
        if (now - lastSpawnTime > config.spawnInterval) {
            items.push(createItem())
            lastSpawnTime = now
        }

        // 更新食材位置
        items = items.filter(item => {
            item.x -= config.moveSpeed * deltaTime  // 基于时间计算移动距离
            return item.x > -config.itemSize
        })
    }

    // 绘制游戏元素
    const drawItems = () => {
        const currentFoodTypes = getCurrentFoodTypes()

        // 检查图片是否都已加载
        const allImagesLoaded = currentFoodTypes.every(food => food.img && food.img.complete) &&
            (gameState.value.useSecondSet ? 
                images.zongzi2.every(zongzi => zongzi && zongzi.complete) :
                images.zongzi.every(zongzi => zongzi && zongzi.complete)) &&
            images.plate && images.plate.complete

        if (!allImagesLoaded) {
            console.error('图片资源未完全加载，无法绘制')
            return
        }

        // 绘制盘子
        config.platePositions.forEach((pos, index) => {
            const plateHeight = config.plateSize * (123/176)
            if (images.plate && images.plate.complete) {
                ctx.drawImage(images.plate, 
                    pos.x - config.plateSize/2, 
                    pos.y - plateHeight/2, 
                    config.plateSize, 
                    plateHeight
                )
            }

            // 绘制盘子下方需要的食材名称
            const requiredFoods = getRequiredFoods(gameState.value.currentZongzi, gameState.value.useSecondSet)
            if (index < requiredFoods.length) {
                const foodType = requiredFoods[index]
                const food = currentFoodTypes[foodType]
                if (food && food.img && food.img.complete) {
                    ctx.fillStyle = gameState.value.collectedItems.includes(foodType) 
                        ? config.foodNameDisplay.colors.collected 
                        : config.foodNameDisplay.colors.normal
                    ctx.font = `${config.foodNameDisplay.fontSize}px ${config.foodNameDisplay.fontFamily}`
                    ctx.textAlign = 'center'
                    ctx.fillText(food.name, pos.x, pos.y + config.foodNameDisplay.offsetY)
                }
            }
        })

        // 绘制当前和下一个要制作的粽子
        const currentZongziIndex = gameState.value.useSecondSet ? 
            gameState.value.currentZongzi - 4 : 
            gameState.value.currentZongzi - 1
        const nextZongziIndex = gameState.value.useSecondSet ? 
            gameState.value.nextZongzi - 4 : 
            gameState.value.nextZongzi - 1

        const currentZongzi = gameState.value.useSecondSet ? 
            images.zongzi2[currentZongziIndex] : 
            images.zongzi[currentZongziIndex]
        const nextZongzi = gameState.value.useSecondSet ? 
            images.zongzi2[nextZongziIndex] : 
            images.zongzi[nextZongziIndex]

        if (currentZongzi && currentZongzi.complete) {
            ctx.drawImage(currentZongzi, 
                config.zongziDisplay.current.x, 
                config.zongziDisplay.current.y, 
                config.zongziDisplay.current.size, 
                config.zongziDisplay.current.size * (currentZongzi.height / currentZongzi.width)
            )
        }

        if (nextZongzi && nextZongzi.complete) {
            ctx.drawImage(nextZongzi, 
                config.zongziDisplay.next.x, 
                config.zongziDisplay.next.y, 
                config.zongziDisplay.next.size, 
                config.zongziDisplay.next.size * (nextZongzi.height / nextZongzi.width)
            )
        }

        // 绘制移动中的食材
        items.forEach(item => {
            const food = currentFoodTypes[item.type]
            if (food && food.img && food.img.complete) {
                ctx.drawImage(food.img, 
                    item.x, item.y, 
                    item.width, item.height
                )
            }
        })

        // 绘制已收集的食材
        gameState.value.collectedItems.forEach((type, index) => {
            if (type !== undefined) {
                // 检查这个位置的食材是否正在动画中
                const isAnimating = gameState.value.animatingItems.some(
                    animItem => animItem.type === type && 
                    Math.abs(animItem.targetX - (config.platePositions[index].x - config.itemSize/2)) < 1
                )
                
                // 只有当食材不在动画中时才绘制在盘子上
                if (!isAnimating) {
                    const food = currentFoodTypes[type]
                    if (food && food.img && food.img.complete) {
                        const platePos = config.platePositions[index]
                        ctx.drawImage(food.img, 
                            platePos.x - config.itemSize/2, 
                            platePos.y - config.itemHeights.food[type]/2 + config.foodOffsetY, 
                            config.itemSize, 
                            config.itemHeights.food[type]
                        )
                    }
                }
            }
        })

        // 绘制动画中的食材
        gameState.value.animatingItems.forEach(item => {
            const food = currentFoodTypes[item.type]
            if (food && food.img && food.img.complete) {
                const progress = (Date.now() - item.startTime) / config.animationDuration
                const currentX = item.startX + (item.targetX - item.startX) * progress
                const currentY = item.startY + (item.targetY - item.startY) * progress
                
                ctx.drawImage(food.img, 
                    currentX, currentY,
                    item.width, item.height
                )
            }
        })

        // 绘制错误提示动画
        gameState.value.errorAnimations.forEach((anim, index) => {
            const progress = (Date.now() - anim.startTime) / 1000  // 1秒的动画时间
            if (progress >= 1) {
                gameState.value.errorAnimations.splice(index, 1)
                return
            }

            ctx.fillStyle = `rgba(255, 0, 0, ${1 - progress})`
            ctx.font = `${config.errorDisplay.fontWeight} ${config.errorDisplay.fontSize}px ${config.errorDisplay.fontFamily}`
            ctx.textAlign = 'center'
            // 根据字号计算移动距离，字号越大移动距离越大
            const moveDistance = config.errorDisplay.offsetY * (1 + config.errorDisplay.fontSize / 30)
            ctx.fillText('-5', anim.x, anim.y - progress * moveDistance)
        })

        // 绘制已收集提示动画
        gameState.value.collectedAnimations.forEach((anim, index) => {
            const progress = (Date.now() - anim.startTime) / 1000  // 1秒的动画时间
            if (progress >= 1) {
                gameState.value.collectedAnimations.splice(index, 1)
                return
            }

            const alpha = 1 - progress
            ctx.font = `${config.collectedDisplay.fontWeight} ${config.collectedDisplay.fontSize}px ${config.collectedDisplay.fontFamily}`
            ctx.textAlign = 'center'
            const moveDistance = config.collectedDisplay.offsetY * (1 + config.collectedDisplay.fontSize / 30)
            
            // 设置描边样式
            ctx.strokeStyle = `rgba(0, 255, 0, ${alpha})`  // 绿色描边，带透明度
            ctx.lineWidth = config.collectedDisplay.strokeWidth
            ctx.strokeText('已收集', anim.x, anim.y - progress * moveDistance)
            
            // 设置填充样式
            ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`  // 白色填充，带透明度
            ctx.fillText('已收集', anim.x, anim.y - progress * moveDistance)
        })

        // 绘制成功提示动画
        gameState.value.successAnimations.forEach((anim, index) => {
            const progress = (Date.now() - anim.startTime) / 1000  // 1秒的动画时间
            if (progress >= 1) {
                gameState.value.successAnimations.splice(index, 1)
                return
            }

            ctx.fillStyle = `rgba(0, 255, 0, ${1 - progress})`
            ctx.font = `${config.successDisplay.fontWeight} ${config.successDisplay.fontSize}px ${config.successDisplay.fontFamily}`
            ctx.textAlign = 'center'
            // 根据字号计算移动距离，字号越大移动距离越大
            const moveDistance = config.successDisplay.offsetY * (1 + config.successDisplay.fontSize / 30)
            ctx.fillText(`+${config.successScore}`, anim.x, anim.y - progress * moveDistance)
        })
    }

    // 检查点击
    const checkClick = (x, y) => {
        // 检查是否点击到食材
        for (let i = items.length - 1; i >= 0; i--) {
            const item = items[i]
            // 计算点击位置与物品中心的距离
            const centerX = item.x + item.width / 2
            const centerY = item.y + item.height / 2
            const distance = Math.sqrt(
                Math.pow(x - centerX, 2) +
                Math.pow(y - centerY, 2)
            )

            // 使用圆形判定区域
            const hitRadius = item.width / 1.5  // 判定半径为物品宽度的2/3
            const hit = distance <= hitRadius

            if (hit) {
                // 检查是否是当前需要的食材
                const requiredFoods = getRequiredFoods(gameState.value.currentZongzi, gameState.value.useSecondSet)
                const isNeeded = requiredFoods.includes(item.type)
                // 检查食材是否已经在任何位置被收集
                const isAlreadyCollected = gameState.value.collectedItems.includes(item.type)
                // 检查目标位置是否已经有其他食材
                const plateIndex = requiredFoods.indexOf(item.type)
                const targetPositionOccupied = gameState.value.collectedItems[plateIndex] !== undefined

                if (isNeeded && !isAlreadyCollected && !targetPositionOccupied) {
                    // 收集食材
                    collectSound.play()
                    const platePos = config.platePositions[plateIndex]
                    const targetX = platePos.x - config.itemSize/2
                    const targetY = platePos.y - config.itemHeights.food[item.type]/2 + config.foodOffsetY
                    
                    // 立即将食材放入对应位置的数组，防止重复点击
                    const newCollectedItems = [...gameState.value.collectedItems]
                    newCollectedItems[plateIndex] = item.type
                    gameState.value.collectedItems = newCollectedItems
                    
                    // 添加动画状态
                    gameState.value.animatingItems.push({
                        type: item.type,
                        startX: item.x,
                        startY: item.y,
                        targetX: targetX,
                        targetY: targetY,
                        width: item.width,
                        height: item.height,
                        startTime: Date.now(),
                    })
                    
                    items.splice(i, 1)
                    setTimeout(() => {
                        // 移除动画状态
                        gameState.value.animatingItems = gameState.value.animatingItems.filter(
                            animItem => animItem.startTime !== gameState.value.animatingItems[0].startTime
                        )
                        
                        // 检查是否可以合成粽子
                        if (checkZongziCompletion()) {
                            console.log('粽子合成成功！', {
                                currentZongzi: gameState.value.currentZongzi,
                                collectedItems: gameState.value.collectedItems,
                                useSecondSet: gameState.value.useSecondSet
                            })
                            gameState.value.score += config.successScore
                            // 添加成功动画
                            const centerX = config.zongziDisplay.current.x + config.zongziDisplay.current.size / 2
                            const centerY = config.zongziDisplay.current.y + config.zongziDisplay.current.size / 2
                            gameState.value.successAnimations.push({
                                x: centerX,
                                y: centerY,
                                startTime: Date.now()
                            })
                            gameState.value.collectedItems = []
                            gameState.value.currentZongzi = gameState.value.nextZongzi
                            gameState.value.nextZongzi = getRandomZongziType(gameState.value.currentZongzi, gameState.value.useSecondSet)
                        }
                    }, config.animationDuration)
                }
                else if (isNeeded && isAlreadyCollected) {
                    // 已收集的食材，显示提示但不扣分
                    gameState.value.collectedAnimations.push({
                        x: centerX,
                        y: centerY,
                        startTime: Date.now()
                    })
                    // 移除已收集的食材
                    items.splice(i, 1)
                }
                else {
                    // 错误选择，扣分
                    gameState.value.score += config.failScore
                    // 添加错误提示动画
                    gameState.value.errorAnimations.push({
                        x: centerX,
                        y: centerY,
                        startTime: Date.now()
                    })
                    // 移除错误的食材
                    items.splice(i, 1)
                }
                break
            }
        }
    }

    // 游戏主循环
    const gameLoop = () => {
        if (!gameState.value.isRunning) return

        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
        updateItems()
        drawItems()

        animationFrame = requestAnimationFrame(gameLoop)
    }

    // 开始计时器
    const startTimer = () => {
        if (timerInterval) clearInterval(timerInterval)

        timerInterval = setInterval(() => {
            if (gameState.value.timer > 0 && gameState.value.isRunning) {
                gameState.value.timer--

                if (gameState.value.timer <= 0) {
                    endGame()
                }
            }
        }, 1000)
    }

    // 事件处理
    const handleClick = (e) => {
        if (!gameState.value.isRunning) return

        if (e.type === 'touchstart') {
            e.preventDefault()
        }

        const point = e.touches ? e.touches[0] : e
        const rect = canvasRef.value.getBoundingClientRect()
        const scaleX = canvasRef.value.width / rect.width
        const scaleY = canvasRef.value.height / rect.height
        const x = (point.clientX - rect.left) * scaleX
        const y = (point.clientY - rect.top) * scaleY

        // console.log('点击坐标：', { x, y })
        // console.log('画布尺寸：', {
        //     width: canvasRef.value.width,
        //     height: canvasRef.value.height,
        //     rectWidth: rect.width,
        //     rectHeight: rect.height
        // })

        checkClick(x, y)
    }

    // 添加事件监听
    const addEvents = () => {
        const canvas = canvasRef.value
        canvas.addEventListener('mousedown', handleClick)
        canvas.addEventListener('touchstart', handleClick, { passive: false })
        
        // 阻止触摸时的页面滚动
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault()
        }, { passive: false })
    }

    // 移除事件监听
    const removeEvents = () => {
        const canvas = canvasRef.value
        canvas.removeEventListener('mousedown', handleClick)
        canvas.removeEventListener('touchstart', handleClick)

        if (animationFrame) {
            cancelAnimationFrame(animationFrame)
        }

        if (timerInterval) {
            clearInterval(timerInterval)
        }

        collectSound.stop()
    }

    // 初始化游戏
    const initGame = async (useSecondSet = false) => {
        if (!canvasRef.value) return

        try {
            ctx = canvasRef.value.getContext('2d')
            items = []
            gameState.value.useSecondSet = useSecondSet
            await loadAssets(useSecondSet)
            addEvents()
            console.log('游戏初始化完成，当前使用食材组:', useSecondSet ? '第二组' : '第一组')
            // 初始化完成后立即开始游戏
            startGame(useSecondSet)
        } catch (error) {
            console.error('游戏初始化失败:', error)
        }
    }

    // 获取随机粽子类型（排除指定类型）
    const getRandomZongziType = (excludeType, useSecondSet = false) => {
        const types = useSecondSet ? [4, 5, 6] : [1, 2, 3]
        let availableTypes = types.filter(type => type !== excludeType)
        return availableTypes[Math.floor(Math.random() * availableTypes.length)]
    }

    // 开始游戏
    const startGame = (useSecondSet = false) => {
        // 确保所有图片都已加载
        const currentFoodTypes = useSecondSet ? foodTypes2 : foodTypes
        const allImagesLoaded = currentFoodTypes.every(food => food.img && food.img.complete) &&
            (gameState.value.useSecondSet ? 
                images.zongzi2.every(zongzi => zongzi && zongzi.complete) :
                images.zongzi.every(zongzi => zongzi && zongzi.complete)) &&
            images.plate && images.plate.complete

        if (!allImagesLoaded) {
            console.error('图片资源未完全加载，无法开始游戏')
            // 检查具体哪些图片未加载完成
            console.log('检查食材图片加载状态:')
            currentFoodTypes.forEach((food, index) => {
                console.log(`${food.name} (${food.image}): ${food.img && food.img.complete ? '已加载' : '未加载'}`)
            })
            console.log('检查粽子图片加载状态:')
            if (gameState.value.useSecondSet) {
                images.zongzi2.forEach((zongzi, index) => {
                    console.log(`第二组粽子${index + 4}: ${zongzi && zongzi.complete ? '已加载' : '未加载'}`)
                })
            } else {
                images.zongzi.forEach((zongzi, index) => {
                    console.log(`粽子${index + 1}: ${zongzi && zongzi.complete ? '已加载' : '未加载'}`)
                })
            }
            console.log(`盘子图片: ${images.plate && images.plate.complete ? '已加载' : '未加载'}`)
            return
        }

        const types = useSecondSet ? [4, 5, 6] : [1, 2, 3]
        const firstZongzi = types[Math.floor(Math.random() * 3)]
        gameState.value = {
            score: 0,
            timer: config.timeLimit,
            isRunning: true,
            isGameOver: false,
            currentZongzi: firstZongzi,
            nextZongzi: getRandomZongziType(firstZongzi, useSecondSet),
            collectedItems: [],
            animatingItems: [],
            errorAnimations: [],
            successAnimations: [],
            collectedAnimations: [],
            useSecondSet: useSecondSet
        }

        items = []
        lastSpawnTime = Date.now()
        lastUpdateTime = Date.now()  // 初始化lastUpdateTime
        startTimer()
        gameLoop()
    }

    // 结束游戏
    const endGame = () => {
        gameState.value.isRunning = false
        gameState.value.isGameOver = true

        if (timerInterval) {
            clearInterval(timerInterval)
        }

        if (onGameEnd) {
            onGameEnd(gameState.value.score >= config.winScore)
        }
    }

    return {
        gameState,
        initGame,
        startGame,
        endGame,
        addEvents,
        removeEvents
    }
}

